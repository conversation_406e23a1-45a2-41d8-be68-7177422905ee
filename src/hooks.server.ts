import type { Handle } from '@sveltejs/kit';

export const handle: Handle = async ({ event, resolve }) => {
  const response = await resolve(event);

  // Only add essential security headers that need app-specific configuration
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');

  // CSP specific to your nHost + GraphQL setup
  response.headers.set('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval'",
    "style-src 'self' 'unsafe-inline'",
    "connect-src 'self' https://*.nhost.run https://*.graphql.us-west-2.nhost.run",
    "img-src 'self' data: https:",
    "font-src 'self' https:"
  ].join('; '));

  return response;
};
