<script lang="ts">
  import { authActions } from '$lib/stores/auth.js';
  import { goto } from '$app/navigation';
  
  import { Button } from '$lib/components/ui/button';

  async function handleSignOut() {
    try {
      await authActions.signOut();
      goto('/auth/login');
    } catch (error) {
      console.error('Sign out error:', error);
    }
  }
</script>

<svelte:head>
  <title>Unauthorized - SourceFlex</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4">
  <div class="w-full max-w-md space-y-8 text-center">
    <div>
      <!-- Lock Icon -->
      <svg class="mx-auto h-16 w-16 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
      </svg>
      
      <h2 class="mt-6 text-3xl font-bold">Access Denied</h2>
      
      <p class="mt-2 text-sm text-muted-foreground">
        You don't have permission to access this page. This may be because:
      </p>
      
      <ul class="mt-4 text-sm text-muted-foreground text-left space-y-1">
        <li>• Your account doesn't have the required role</li>
        <li>• You need to be assigned to an organization</li>
        <li>• Your account is inactive</li>
        <li>• Your session has expired</li>
      </ul>
    </div>

    <div class="mt-8 space-y-4">
      <a href="/dashboard" class="w-full">
        <Button class="w-full">Go to Dashboard</Button>
      </a>
      
      <Button variant="outline" onclick={handleSignOut} class="w-full">
        Sign Out
      </Button>
    </div>

    <div class="mt-6 text-xs text-muted-foreground">
      If you believe this is an error, please contact your administrator.
    </div>
  </div>
</div>
