<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Card, CardHeader, CardTitle, CardContent } from '$lib/components/ui/card';
  import { Input } from '$lib/components/ui/input';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';

  // Get job ID from URL params
  $: jobId = $page.params.id;

  // Mock job details based on ID
  const mockJobDetails = {
    '1': {
      id: '1',
      title: 'Senior Frontend Developer',
      company: 'TechCorp Inc',
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      status: 'Open',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      posted: '2 days ago',
      description: 'We are looking for a Senior Frontend Developer to join our growing team. You will be responsible for building user-facing features using React, TypeScript, and modern frontend technologies.',
      requirements: [
        '5+ years of frontend development experience',
        'Expert knowledge of React and TypeScript',
        'Experience with modern build tools (Vite, Webpack)',
        'Strong CSS skills and responsive design',
        'Experience with testing frameworks (Jest, Cypress)'
      ],
      benefits: [
        'Competitive salary and equity',
        'Health, dental, and vision insurance',
        'Remote work flexibility',
        'Professional development budget',
        '401k matching'
      ]
    }
  };

  $: currentJob = mockJobDetails[jobId] || mockJobDetails['1'];

  function goBack() {
    goto('/demopages/traditional');
  }

  function handleEdit() {
    // Mock edit action
    alert('Edit functionality would be implemented here');
  }

  function handleApply() {
    // Mock apply action  
    alert('Apply functionality would be implemented here');
  }
</script>

<svelte:head>
  <title>{currentJob.title} - Job Details</title>
</svelte:head>

<div class="space-y-6">
  <!-- Back Navigation -->
  <div class="flex items-center gap-3">
    <Button variant="outline" size="sm" on:click={goBack}>
      ← Back to Jobs
    </Button>
    <span class="text-muted-foreground text-sm">Job Details</span>
  </div>

  <!-- Job Header -->
  <Card>
    <CardHeader>
      <div class="flex justify-between items-start">
        <div>
          <CardTitle class="text-2xl">{currentJob.title}</CardTitle>
          <p class="text-lg text-muted-foreground mt-1">{currentJob.company}</p>
        </div>
        <div class="flex gap-2">
          <Button variant="outline" size="sm" on:click={handleEdit}>
            Edit Job
          </Button>
          <Button size="sm" on:click={handleApply}>
            Apply Now
          </Button>
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
        <div>
          <span class="font-medium text-muted-foreground">Location:</span>
          <p>{currentJob.location}</p>
        </div>
        <div>
          <span class="font-medium text-muted-foreground">Salary:</span>
          <p>{currentJob.salary}</p>
        </div>
        <div>
          <span class="font-medium text-muted-foreground">Type:</span>
          <p>{currentJob.type}</p>
        </div>
        <div>
          <span class="font-medium text-muted-foreground">Experience:</span>
          <p>{currentJob.experience}</p>
        </div>
      </div>
      
      <div class="flex gap-2 mt-4">
        <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
          {currentJob.status}
        </span>
        <span class="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
          Priority {currentJob.priority}
        </span>
      </div>
    </CardContent>
  </Card>

  <!-- Job Description -->
  <Card>
    <CardHeader>
      <CardTitle>Job Description</CardTitle>
    </CardHeader>
    <CardContent>
      <p class="text-muted-foreground leading-relaxed">
        {currentJob.description}
      </p>
    </CardContent>
  </Card>

  <!-- Requirements & Benefits Grid -->
  <div class="grid md:grid-cols-2 gap-6">
    <!-- Requirements -->
    <Card>
      <CardHeader>
        <CardTitle>Requirements</CardTitle>
      </CardHeader>
      <CardContent>
        <ul class="space-y-2">
          {#each currentJob.requirements as requirement}
            <li class="flex items-start gap-2 text-sm">
              <span class="text-green-600 mt-1">✓</span>
              <span>{requirement}</span>
            </li>
          {/each}
        </ul>
      </CardContent>
    </Card>

    <!-- Benefits -->
    <Card>
      <CardHeader>
        <CardTitle>Benefits</CardTitle>
      </CardHeader>
      <CardContent>
        <ul class="space-y-2">
          {#each currentJob.benefits as benefit}
            <li class="flex items-start gap-2 text-sm">
              <span class="text-blue-600 mt-1">★</span>
              <span>{benefit}</span>
            </li>
          {/each}
        </ul>
      </CardContent>
    </Card>
  </div>

  <!-- Action Form -->
  <Card>
    <CardHeader>
      <CardTitle>Quick Actions</CardTitle>
    </CardHeader>
    <CardContent>
      <div class="space-y-4">
        <div class="grid md:grid-cols-3 gap-4">
          <div>
            <label class="text-sm font-medium">Change Status</label>
            <select class="w-full mt-1 px-3 py-2 border border-input rounded-md">
              <option value="open" selected={currentJob.status === 'Open'}>Open</option>
              <option value="draft">Draft</option>
              <option value="filled">Filled</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div>
            <label class="text-sm font-medium">Assign To</label>
            <Input placeholder="Search recruiters..." class="mt-1" />
          </div>
          <div>
            <label class="text-sm font-medium">Add Note</label>
            <Input placeholder="Internal note..." class="mt-1" />
          </div>
        </div>
        <Button size="sm" class="w-full md:w-auto">
          Save Changes
        </Button>
      </div>
    </CardContent>
  </Card>

  <!-- Pattern Demo Note -->
  <Card class="border-orange-200 bg-orange-50">
    <CardContent class="pt-6">
      <p class="text-sm text-orange-800">
        <strong>Traditional Pattern Demo:</strong> This dedicated page provides full screen space for complex forms, 
        detailed information, and focused workflows. Users get a bookmarkable URL and can use browser 
        back/forward navigation naturally.
      </p>
    </CardContent>
  </Card>
</div>
