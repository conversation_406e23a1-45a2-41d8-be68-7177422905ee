<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { isAuthenticated } from '$lib/stores/auth.js';

	// Only redirect authenticated users to dashboard
	// Show public homepage for everyone else
	onMount(() => {
		const unsubscribe = isAuthenticated.subscribe((authenticated) => {
			if (authenticated) {
				goto('/dashboard');
			}
			// Don't redirect unauthenticated users - show homepage
		});

		return unsubscribe;
	});
</script>

<svelte:head>
	<title>SourceFlex - Talent Acquisition Platform</title>
	<meta name="description" content="Streamline your recruitment and bench sales process with SourceFlex" />
</svelte:head>

<!-- Public Homepage -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
	<!-- Navigation -->
	<nav class="bg-white shadow-sm">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
			<div class="flex justify-between h-16">
				<div class="flex items-center">
					<h1 class="text-2xl font-bold text-blue-600">SourceFlex</h1>
				</div>
				<div class="flex items-center space-x-4">
					<a href="/auth/login" class="text-gray-700 hover:text-blue-600 px-3 py-2 rounded-md text-sm font-medium">
						Sign In
					</a>
					<a href="/auth/register" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
						Get Started
					</a>
				</div>
			</div>
		</div>
	</nav>

	<!-- Hero Section -->
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
		<div class="text-center">
			<h1 class="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
				Streamline Your
				<span class="text-blue-600">Talent Acquisition</span>
			</h1>
			<p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
				Unify your recruitment and bench sales operations in one powerful platform. 
				Manage candidates, track placements, and grow your business efficiently.
			</p>
			<div class="flex flex-col sm:flex-row gap-4 justify-center">
				<a href="/auth/register" 
					class="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg text-lg font-semibold shadow-lg transition-colors">
					Start Free Trial
				</a>
				<a href="/auth/login" 
					class="border-2 border-blue-600 text-blue-600 hover:bg-blue-50 px-8 py-3 rounded-lg text-lg font-semibold transition-colors">
					Sign In
				</a>
			</div>
		</div>
	</div>

	<!-- Features Section -->
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
		<div class="grid md:grid-cols-3 gap-8">
			<div class="bg-white p-6 rounded-lg shadow-sm">
				<div class="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
					<svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
					</svg>
				</div>
				<h3 class="text-xl font-semibold text-gray-900 mb-2">Unified Recruitment</h3>
				<p class="text-gray-600">Manage your entire recruitment pipeline from sourcing to placement in one integrated platform.</p>
			</div>

			<div class="bg-white p-6 rounded-lg shadow-sm">
				<div class="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
					<svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
					</svg>
				</div>
				<h3 class="text-xl font-semibold text-gray-900 mb-2">Bench Sales Management</h3>
				<p class="text-gray-600">Track your bench resources, manage submissions, and optimize placements with intelligent matching.</p>
			</div>

			<div class="bg-white p-6 rounded-lg shadow-sm">
				<div class="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
					<svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
					</svg>
				</div>
				<h3 class="text-xl font-semibold text-gray-900 mb-2">Enterprise Grade</h3>
				<p class="text-gray-600">Secure, scalable, and reliable platform built for growing recruitment businesses.</p>
			</div>
		</div>
	</div>

	<!-- Footer -->
	<footer class="bg-white border-t">
		<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
			<div class="text-center text-gray-600">
				<p>&copy; 2025 SourceFlex. All rights reserved.</p>
			</div>
		</div>
	</footer>
</div>
