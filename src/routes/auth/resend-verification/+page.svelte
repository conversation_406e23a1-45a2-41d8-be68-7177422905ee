<script lang="ts">
  import { goto } from '$app/navigation';
  import { nhost } from '$lib/stores/nhost.js';
  import toast from 'svelte-5-french-toast';
  import { browser } from '$app/environment';
  
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';

  let email = $state('');
  let isLoading = $state(false);
  let isEmailSent = $state(false);
  let emailError = $state('');
  let attemptCount = $state(0);
  let maxAttempts = 3;
  let rateLimitKey = '';

  // Rate limiting using localStorage (client-side only)
  function getRateLimitKey() {
    const today = new Date().toDateString();
    return `verification_attempts_${today}`;
  }

  function checkRateLimit(): boolean {
    if (!browser) return true;
    
    rateLimitKey = getRateLimitKey();
    const stored = localStorage.getItem(rateLimitKey);
    attemptCount = stored ? parseInt(stored) : 0;
    
    return attemptCount < maxAttempts;
  }

  function incrementAttemptCount() {
    if (!browser) return;
    
    attemptCount++;
    localStorage.setItem(rateLimitKey, attemptCount.toString());
  }

  // Real-time email validation
  $effect(() => {
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      emailError = !emailRegex.test(email) ? 'Please enter a valid email address' : '';
    } else {
      emailError = '';
    }
  });

  async function handleSubmit(event: Event) {
    event.preventDefault();

    if (!email || emailError) {
      toast.error('Please enter a valid email address');
      return;
    }

    // Check rate limit
    if (!checkRateLimit()) {
      toast.error(`You've reached the daily limit of ${maxAttempts} verification attempts. Please contact support if you need assistance.`);
      return;
    }

    isLoading = true;

    try {
      console.log('Sending verification email to:', email);
      
      // Use nHost to resend verification email
      const result = await nhost.auth.signUp({
        email: email,
        password: '', // Empty password for resend
        options: {
          allowSignUp: false // This should trigger resend for existing users
        }
      });

      console.log('Resend verification result:', result);

      // Increment attempt count regardless of result
      incrementAttemptCount();

      if (result.error) {
        // Some errors are expected (like user already exists)
        if (result.error.message?.includes('User already registered')) {
          // This is actually what we want - it should trigger a resend
          isEmailSent = true;
          toast.success('Verification email sent! Please check your inbox.');
        } else {
          console.error('Resend verification error:', result.error);
          toast.error(result.error.message || 'Failed to send verification email');
        }
      } else {
        isEmailSent = true;
        toast.success('Verification email sent! Please check your inbox.');
      }

    } catch (error) {
      console.error('Resend verification error:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      isLoading = false;
    }
  }

  function handleBackToLogin() {
    goto('/auth/login');
  }

  // Initialize rate limit check
  if (browser) {
    checkRateLimit();
  }
</script>

<svelte:head>
  <title>Resend Verification - SourceFlex</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4">
  <div class="w-full max-w-md space-y-8">
    <div class="text-center">
      <h2 class="text-3xl font-bold">Resend Verification Email</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Enter your email address to receive a new verification link
      </p>
    </div>

    {#if isEmailSent}
      <!-- Success State -->
      <div class="border border-green-200 bg-green-50 rounded-lg p-4">
        <div class="flex items-start">
          <svg class="h-5 w-5 text-green-400 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-green-800 mb-2">Verification email sent!</h3>
            <p class="text-sm text-green-700">
              We've sent a new verification link to <strong>{email}</strong>. 
              Please check your inbox and spam folder.
            </p>
            <div class="mt-3">
              <button
                onclick={handleBackToLogin}
                class="text-sm font-medium text-green-800 hover:underline"
              >
                Back to Login
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Email Tips -->
      <div class="border border-blue-200 bg-blue-50 rounded-lg p-4">
        <div class="flex items-start">
          <svg class="h-5 w-5 text-blue-400 mt-0.5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800 mb-2">Tips for receiving your email:</h3>
            <ul class="text-sm text-blue-700 space-y-1">
              <li>• Check your spam/junk folder</li>
              <li>• Wait up to 5 minutes for delivery</li>
              <li>• Add <EMAIL> to your contacts</li>
              <li>• Check that your email address is correct</li>
            </ul>
          </div>
        </div>
      </div>

    {:else}
      <!-- Resend Form -->
      <form class="space-y-6" onsubmit={handleSubmit}>
        <div class="space-y-2">
          <label for="email" class="text-sm font-medium">Email address</label>
          <Input
            id="email"
            type="email"
            bind:value={email}
            placeholder="Enter your email address"
            disabled={isLoading}
            class={emailError ? 'border-red-500' : ''}
          />
          {#if emailError}
            <p class="text-sm text-red-600">{emailError}</p>
          {/if}
        </div>

        <!-- Rate Limiting Info -->
        {#if attemptCount > 0}
          <div class="text-sm text-muted-foreground bg-muted p-3 rounded-md">
            <p>Attempts used today: {attemptCount}/{maxAttempts}</p>
            {#if attemptCount >= maxAttempts}
              <p class="text-red-600 font-medium">
                Daily limit reached. Please contact support for assistance.
              </p>
            {/if}
          </div>
        {/if}

        <Button 
          type="submit" 
          class="w-full" 
          disabled={isLoading || emailError !== '' || attemptCount >= maxAttempts}
        >
          {#if isLoading}
            <svg class="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Sending...
          {:else if attemptCount >= maxAttempts}
            Daily Limit Reached
          {:else}
            Send Verification Email
          {/if}
        </Button>

        <div class="text-center space-y-2">
          <button
            type="button"
            onclick={handleBackToLogin}
            class="text-sm font-medium text-primary hover:underline"
          >
            Back to Login
          </button>
          
          <p class="text-xs text-muted-foreground">
            Need help? 
            <a href="mailto:<EMAIL>" class="font-medium text-primary hover:underline">
              Contact Support
            </a>
          </p>
        </div>
      </form>
    {/if}
  </div>
</div>
