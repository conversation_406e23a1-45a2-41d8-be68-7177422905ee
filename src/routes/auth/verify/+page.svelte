<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { onMount } from 'svelte';
  import { nhost } from '$lib/stores/nhost.js';
  import toast from 'svelte-5-french-toast';
  
  import { Button } from '$lib/components/ui/button';

  let isProcessing = $state(true);
  let verificationStatus = $state<'processing' | 'success' | 'failed' | 'already_verified' | 'invalid'>('processing');
  let userEmail = $state('');
  let errorMessage = $state('');

  onMount(async () => {
    await handleVerification();
  });

  async function handleVerification() {
    try {
      // Get verification parameters from URL
      const token = $page.url.searchParams.get('refreshToken');
      const type = $page.url.searchParams.get('type');

      console.log('Verification attempt:', { token: token?.substring(0, 8) + '...', type });

      if (!token || type !== 'verifyEmail') {
        verificationStatus = 'invalid';
        errorMessage = 'Invalid verification link';
        isProcessing = false;
        return;
      }

      // Attempt verification with nHost
      const result = await nhost.auth.verifyEmail({
        refreshToken: token
      });

      console.log('Verification result:', result);

      if (result.error) {
        console.error('Verification failed:', result.error);
        
        // Handle different error types
        if (result.error.message?.includes('expired') || result.error.message?.includes('invalid')) {
          // Check if account exists and is already verified
          await checkAccountStatus(token);
        } else {
          verificationStatus = 'failed';
          errorMessage = result.error.message || 'Verification failed';
        }
      } else {
        // Verification successful - but don't stay logged in
        verificationStatus = 'success';
        userEmail = result.session?.user?.email || '';
        
        // Sign out immediately to prevent auto-dashboard redirect
        await nhost.auth.signOut();
        
        // Redirect to login after short delay to show success message
        setTimeout(() => {
          redirectToLogin('verified');
        }, 2000);
      }

    } catch (error) {
      console.error('Verification error:', error);
      verificationStatus = 'failed';
      errorMessage = 'An unexpected error occurred';
    } finally {
      isProcessing = false;
    }
  }

  async function checkAccountStatus(token: string) {
    try {
      // Try to extract email from token or check account status
      // For now, we'll assume the account exists but token is expired
      verificationStatus = 'failed';
      errorMessage = 'Verification link has expired';
    } catch (error) {
      console.error('Account status check failed:', error);
      verificationStatus = 'failed';
      errorMessage = 'Verification link is invalid';
    }
  }

  function redirectToLogin(reason: string) {
    const params = new URLSearchParams();
    params.set('message', reason);
    if (userEmail) {
      params.set('email', userEmail);
    }
    goto(`/auth/login?${params.toString()}`);
  }

  function handleResendVerification() {
    goto('/auth/resend-verification');
  }
</script>

<svelte:head>
  <title>Email Verification - SourceFlex</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4">
  <div class="w-full max-w-md space-y-8">
    <div class="text-center">
      {#if isProcessing}
        <!-- Processing State -->
        <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-primary mx-auto mb-4"></div>
        <h2 class="text-2xl font-bold mb-2">Verifying your email...</h2>
        <p class="text-muted-foreground">Please wait while we verify your account.</p>
        
      {:else if verificationStatus === 'success'}
        <!-- Success State -->
        <div class="rounded-full h-16 w-16 bg-green-100 mx-auto mb-4 flex items-center justify-center">
          <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-green-600 mb-2">Email Verified Successfully!</h2>
        <p class="text-muted-foreground mb-4">
          Your email has been verified. You will be redirected to login shortly.
        </p>
        <Button onclick={() => redirectToLogin('verified')} class="bg-green-600 hover:bg-green-700">
          Continue to Login
        </Button>
        
      {:else if verificationStatus === 'already_verified'}
        <!-- Already Verified State -->
        <div class="rounded-full h-16 w-16 bg-blue-100 mx-auto mb-4 flex items-center justify-center">
          <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-blue-600 mb-2">Already Verified</h2>
        <p class="text-muted-foreground mb-4">
          Your email is already verified. Please log in to access your account.
        </p>
        <Button onclick={() => redirectToLogin('already_verified')}>
          Go to Login
        </Button>
        
      {:else if verificationStatus === 'failed'}
        <!-- Failed State -->
        <div class="rounded-full h-16 w-16 bg-red-100 mx-auto mb-4 flex items-center justify-center">
          <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-red-600 mb-2">Verification Failed</h2>
        <p class="text-muted-foreground mb-4">{errorMessage}</p>
        
        <div class="space-y-3">
          <Button onclick={handleResendVerification} class="w-full">
            Get New Verification Link
          </Button>
          
          <Button variant="outline" onclick={() => redirectToLogin('failed')} class="w-full">
            Go to Login
          </Button>
        </div>
        
      {:else if verificationStatus === 'invalid'}
        <!-- Invalid Link State -->
        <div class="rounded-full h-16 w-16 bg-yellow-100 mx-auto mb-4 flex items-center justify-center">
          <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <h2 class="text-2xl font-bold text-yellow-600 mb-2">Invalid Link</h2>
        <p class="text-muted-foreground mb-4">
          This verification link is invalid or malformed.
        </p>
        
        <div class="space-y-3">
          <Button onclick={() => goto('/auth/register')} class="w-full">
            Create New Account
          </Button>
          
          <Button variant="outline" onclick={handleResendVerification} class="w-full">
            Resend Verification
          </Button>
        </div>
      {/if}
    </div>

    <!-- Support Link -->
    <div class="text-center mt-6">
      <p class="text-xs text-muted-foreground">
        Having trouble? 
        <a href="mailto:<EMAIL>" class="font-medium text-primary hover:underline">
          Contact Support
        </a>
      </p>
    </div>
  </div>
</div>
