<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import * as DropdownMenu from "$lib/components/ui/dropdown-menu";
  import { Filter, Plus, Settings } from "lucide-svelte/icons";

  // Search and filter state
  let searchQuery = $state("");
  let showFilterModal = $state(false);
  let showColumnMenu = $state(false);

  // Placeholder functions
  function openFilterModal() {
    showFilterModal = true;
  }

  function closeFilterModal() {
    showFilterModal = false;
  }

  function toggleColumnMenu() {
    showColumnMenu = !showColumnMenu;
  }

  function addNewJob() {
    console.log("Add new job clicked");
  }
</script>

<svelte:head>
  <title>Jobs Management</title>
</svelte:head>

<!-- Responsive Header Layout -->
<div class="min-h-screen bg-background">
  <div class="px-4 py-6">
    
    <!-- Header Layout -->
    <div class="space-y-4">
      
      <!-- Desktop Layout: [Jobs] [Search][Filter] [Columns][+] -->
      <div class="hidden md:flex md:items-center md:justify-between">
        
        <!-- Left: Module Name -->
        <div>
          <h1 class="text-2xl font-bold">Jobs</h1>
        </div>
        
        <!-- Center: Search + Filter -->
        <div class="flex items-center space-x-2 flex-1 justify-center max-w-md">
          <Input
            bind:value={searchQuery}
            placeholder="Search jobs..."
            class="flex-1"
          />
          <Button 
            variant="outline" 
            size="sm" 
            onclick={openFilterModal}
          >
            <Filter class="h-4 w-4" />
          </Button>
        </div>
        
        <!-- Right: Actions -->
        <div class="flex items-center space-x-2">
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <Button variant="outline" size="sm">
                <Settings class="h-4 w-4 mr-2" />
                Columns
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item>All Columns</DropdownMenu.Item>
              <DropdownMenu.Item>Hide Status</DropdownMenu.Item>
              <DropdownMenu.Item>Hide Priority</DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
          
          <Button size="sm" onclick={addNewJob}>
            <Plus class="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      <!-- Mobile Layout -->
      <div class="md:hidden space-y-3">
        <!-- First Row: Search + Filter -->
        <div class="flex items-center space-x-2">
          <Input
            bind:value={searchQuery}
            placeholder="Search jobs..."
            class="flex-1"
          />
          <Button 
            variant="outline" 
            size="sm" 
            onclick={openFilterModal}
          >
            <Filter class="h-4 w-4" />
          </Button>
        </div>
        
        <!-- Second Row: Columns + Bulk Actions -->
        <div class="flex items-center justify-between">
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              <Button variant="outline" size="sm">
                <Settings class="h-4 w-4 mr-2" />
                Columns
              </Button>
            </DropdownMenu.Trigger>
            <DropdownMenu.Content>
              <DropdownMenu.Item>All Columns</DropdownMenu.Item>
              <DropdownMenu.Item>Hide Status</DropdownMenu.Item>
              <DropdownMenu.Item>Hide Priority</DropdownMenu.Item>
            </DropdownMenu.Content>
          </DropdownMenu.Root>
          
          <!-- Bulk actions will appear here when rows selected -->
          <div class="text-sm text-muted-foreground">
            <!-- Future: Bulk actions -->
          </div>
        </div>
      </div>
    </div>
    
    <!-- Table Area -->
    <div class="mt-6">
      <div class="border rounded-lg bg-card">
        <div class="p-8 text-center">
          <div class="space-y-2">
            <h2 class="text-lg font-semibold">Jobs Table</h2>
            <p class="text-sm text-muted-foreground">Real API integration coming next...</p>
          </div>
        </div>
      </div>
    </div>
    
  </div>
</div>

<!-- Filter Modal -->
{#if showFilterModal}
  <div class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- Backdrop -->
    <div 
      class="absolute inset-0 bg-black/50" 
      onclick={closeFilterModal}
    ></div>
    
    <!-- Modal -->
    <div class="relative bg-white rounded-lg p-6 w-full max-w-md mx-4 shadow-lg">
      <div class="space-y-4">
        <div class="flex items-center justify-between">
          <h3 class="text-lg font-semibold">Filters</h3>
          <Button variant="ghost" size="sm" onclick={closeFilterModal}>
            ×
          </Button>
        </div>
        
        <div class="space-y-4">
          <p class="text-sm text-muted-foreground">
            Filter options will be added with real API data...
          </p>
        </div>
        
        <div class="flex justify-end space-x-2">
          <Button variant="outline" onclick={closeFilterModal}>Cancel</Button>
          <Button onclick={closeFilterModal}>Apply</Button>
        </div>
      </div>
    </div>
  </div>
{/if}