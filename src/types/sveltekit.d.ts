// SvelteKit module declarations
// This file provides type declarations for SvelteKit modules

declare module '$app/forms' {
	/**
	 * Enhance a form with progressive enhancement
	 */
	export function enhance(
		form: HTMLFormElement,
		submit?: (input: {
			formData: FormData;
			formElement: HTMLFormElement;
			action: URL;
			cancel(): void;
			submitter: HTMLElement | null;
		}) => void | ((opts: { 
			result: any; 
			update: (opts?: { reset?: boolean }) => void 
		}) => void)
	): { destroy(): void };
}

declare module '$app/navigation' {
	/**
	 * Navigate to a new URL
	 */
	export function goto(
		url: string | URL, 
		opts?: { 
			replaceState?: boolean; 
			noScroll?: boolean; 
			keepFocus?: boolean; 
			invalidateAll?: boolean; 
			state?: any 
		}
	): Promise<void>;

	/**
	 * Invalidate data for a specific dependency
	 */
	export function invalidate(dependency: string | URL | ((url: URL) => boolean)): Promise<void>;

	/**
	 * Invalidate all data
	 */
	export function invalidateAll(): Promise<void>;

	/**
	 * Preload data for a route
	 */
	export function preloadData(href: string): Promise<void>;

	/**
	 * Preload code for routes
	 */
	export function preloadCode(...urls: string[]): Promise<void>;

	/**
	 * Register a callback to run before navigation
	 */
	export function beforeNavigate(fn: (navigation: { 
		from: URL | null; 
		to: URL | null; 
		type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate'; 
		willUnload: boolean; 
		cancel(): void 
	}) => void): void;

	/**
	 * Register a callback to run after navigation
	 */
	export function afterNavigate(fn: (navigation: { 
		from: URL | null; 
		to: URL | null; 
		type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate' 
	}) => void): void;

	/**
	 * Register a callback to run during navigation
	 */
	export function onNavigate(fn: (navigation: { 
		from: URL | null; 
		to: URL | null; 
		type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate'; 
		willUnload: boolean; 
		complete: Promise<void> 
	}) => void | (() => void)): void;

	/**
	 * Disable scroll handling for the next navigation
	 */
	export function disableScrollHandling(): void;

	/**
	 * Push a new state to the history stack
	 */
	export function pushState(url: string | URL, state: any): void;

	/**
	 * Replace the current state in the history stack
	 */
	export function replaceState(url: string | URL, state: any): void;
}

declare module '$app/stores' {
	import type { Readable } from 'svelte/store';

	/**
	 * A readable store containing the current page data
	 */
	export const page: Readable<{
		url: URL;
		params: Record<string, string>;
		route: { id: string | null };
		status: number;
		error: Error | null;
		data: Record<string, any>;
		form: any;
		state: Record<string, any>;
	}>;

	/**
	 * A readable store containing the current navigation state
	 */
	export const navigating: Readable<{
		from: URL | null;
		to: URL | null;
		type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate';
		willUnload: boolean;
		delta?: number;
		complete: Promise<void>;
	} | null>;

	/**
	 * A readable store indicating whether the app is currently updating
	 */
	export const updated: Readable<boolean>;
}

declare module '$app/environment' {
	/**
	 * Whether the app is running in the browser
	 */
	export const browser: boolean;

	/**
	 * Whether the app is running in development mode
	 */
	export const dev: boolean;

	/**
	 * Whether the app is running in a Node.js environment
	 */
	export const building: boolean;

	/**
	 * The version of the app
	 */
	export const version: string;
}

declare module '$app/paths' {
	/**
	 * The base path of the app
	 */
	export const base: string;

	/**
	 * The assets path
	 */
	export const assets: string;
}

// Additional module declarations for missing dependencies
declare module 'svelte-french-toast' {
	interface ToastOptions {
		id?: string;
		duration?: number;
		position?: string;
		style?: string;
		className?: string;
		icon?: string;
		iconTheme?: any;
		ariaProps?: any;
	}

	interface Toast {
		success(message: string, options?: ToastOptions): string;
		error(message: string, options?: ToastOptions): string;
		loading(message: string, options?: ToastOptions): string;
		custom(jsx: any, options?: ToastOptions): string;
		dismiss(toastId?: string): void;
		remove(toastId?: string): void;
		promise<T>(
			promise: Promise<T>,
			msgs: {
				loading: string;
				success: string | ((data: T) => string);
				error: string | ((error: any) => string);
			},
			options?: ToastOptions
		): Promise<T>;
	}

	const toast: Toast;
	export default toast;
}

declare module 'svelte-5-french-toast' {
	interface ToastOptions {
		id?: string;
		duration?: number;
		position?: string;
		style?: string;
		className?: string;
		icon?: string;
		iconTheme?: any;
		ariaProps?: any;
	}

	interface Toast {
		success(message: string, options?: ToastOptions): string;
		error(message: string, options?: ToastOptions): string;
		loading(message: string, options?: ToastOptions): string;
		custom(jsx: any, options?: ToastOptions): string;
		dismiss(toastId?: string): void;
		remove(toastId?: string): void;
		promise<T>(
			promise: Promise<T>,
			msgs: {
				loading: string;
				success: string | ((data: T) => string);
				error: string | ((error: any) => string);
			},
			options?: ToastOptions
		): Promise<T>;
	}

	const toast: Toast;
	export default toast;
}
