// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces
import type { KVNamespace, DurableObjectNamespace } from '@cloudflare/workers-types';

declare global {
	namespace App {
		// interface Error {}
		// interface Locals {}
		// interface PageData {}
		// interface PageState {}
		interface Platform {
			env?: {
				// Add your Cloudflare bindings here
				// Example: YOUR_KV_NAMESPACE: KVNamespace;
				// Example: YOUR_DURABLE_OBJECT_NAMESPACE: DurableObjectNamespace;
			};
			ctx?: ExecutionContext;
			caches?: CacheStorage;
			cf?: IncomingRequestCfProperties;
		}
	}
}

// Manual type declarations for SvelteKit $app modules
declare module '$app/forms' {
	export function enhance(
		form: HTMLFormElement,
		submit?: (input: {
			formData: FormData;
			formElement: HTMLFormElement;
			action: URL;
			cancel(): void;
			submitter: HTMLElement | null;
		}) => void | ((opts: { result: any; update: (opts?: { reset?: boolean }) => void }) => void)
	): { destroy(): void };
}

declare module '$app/navigation' {
	export function goto(url: string | URL, opts?: { replaceState?: boolean; noScroll?: boolean; keepFocus?: boolean; invalidateAll?: boolean; state?: any }): Promise<void>;
	export function invalidate(dependency: string | URL | ((url: URL) => boolean)): Promise<void>;
	export function invalidateAll(): Promise<void>;
	export function preloadData(href: string): Promise<void>;
	export function preloadCode(...urls: string[]): Promise<void>;
	export function beforeNavigate(fn: (navigation: { from: URL | null; to: URL | null; type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate'; willUnload: boolean; cancel(): void }) => void): void;
	export function afterNavigate(fn: (navigation: { from: URL | null; to: URL | null; type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate' }) => void): void;
	export function onNavigate(fn: (navigation: { from: URL | null; to: URL | null; type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate'; willUnload: boolean; complete: Promise<void> }) => void | (() => void)): void;
	export function disableScrollHandling(): void;
	export function pushState(url: string | URL, state: any): void;
	export function replaceState(url: string | URL, state: any): void;
}

export {};
