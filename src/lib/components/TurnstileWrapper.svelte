<script lang="ts">
import Turnstile from './Turnstile.svelte';
import { createEventDispatcher } from 'svelte';

export let showTitle = true;
export let title = 'Security Verification';
export let description = 'Please verify you\'re human to continue.';
export let theme: 'light' | 'dark' | 'auto' = 'auto';
export let size: 'normal' | 'compact' = 'normal';

const dispatch = createEventDispatcher();

let isLoading = true;
let isVerified = false;
let hasError = false;
let errorMessage = '';

const handleSuccess = (token: string) => {
  isVerified = true;
  isLoading = false;
  dispatch('verified', { token });
};

const handleError = (error: string) => {
  hasError = true;
  isLoading = false;
  errorMessage = error;
  dispatch('error', { error });
};

const handleExpired = () => {
  isVerified = false;
  hasError = false;
  isLoading = true;
  dispatch('expired');
};

const retry = () => {
  hasError = false;
  isLoading = true;
  // The Turnstile component will automatically retry
};
</script>

<div class="turnstile-wrapper">
  {#if showTitle}
    <div class="verification-header">
      <h3 class="verification-title">{title}</h3>
      <p class="verification-description">{description}</p>
    </div>
  {/if}

  <div class="verification-content">
    {#if isLoading && !hasError}
      <div class="loading-state">
        <div class="spinner"></div>
        <p>Loading security verification...</p>
      </div>
    {/if}

    {#if hasError}
      <div class="error-state">
        <div class="error-icon">⚠️</div>
        <p class="error-message">{errorMessage}</p>
        <button class="retry-button" onclick={retry}>
          Try Again
        </button>
      </div>
    {:else if isVerified}
      <div class="success-state">
        <div class="success-icon">✅</div>
        <p>Verification successful!</p>
      </div>
    {:else}
      <div class="turnstile-container">
        <Turnstile 
          {theme} 
          {size}
          onSuccess={handleSuccess}
          onError={handleError}
          onExpired={handleExpired}
        />
      </div>
    {/if}
  </div>
</div>

<style>
  .turnstile-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.5rem;
    background: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    margin: 0 auto;
  }

  .verification-header {
    text-align: center;
    margin-bottom: 1.5rem;
  }

  .verification-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }

  .verification-description {
    color: #6b7280;
    margin: 0;
    font-size: 0.875rem;
  }

  .verification-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 2rem 0;
  }

  .spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid #e2e8f0;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  .loading-state p {
    color: #6b7280;
    font-size: 0.875rem;
    margin: 0;
  }

  .error-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 0;
  }

  .error-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .error-message {
    color: #dc2626;
    font-size: 0.875rem;
    text-align: center;
    margin: 0 0 1rem 0;
  }

  .retry-button {
    background: #3b82f6;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s;
  }

  .retry-button:hover {
    background: #2563eb;
  }

  .success-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 0;
  }

  .success-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
  }

  .success-state p {
    color: #059669;
    font-size: 0.875rem;
    font-weight: 500;
    margin: 0;
  }

  .turnstile-container {
    width: 100%;
    display: flex;
    justify-content: center;
    min-height: 65px;
  }

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    .turnstile-wrapper {
      background: #1f2937;
      border-color: #374151;
    }

    .verification-title {
      color: #f9fafb;
    }

    .verification-description {
      color: #9ca3af;
    }

    .loading-state p {
      color: #9ca3af;
    }

    .spinner {
      border-color: #374151;
      border-top-color: #60a5fa;
    }
  }
</style>
