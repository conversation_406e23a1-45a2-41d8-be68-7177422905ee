<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { authActions, isAuthenticated } from '$lib/stores/auth.js';
  import { onMount } from 'svelte';
  import toast from 'svelte-5-french-toast';
  
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Checkbox } from '$lib/components/ui/checkbox';
  import TurnstileWrapper from '$lib/components/TurnstileWrapper.svelte';

  let email = $state('');
  let password = $state('');
  let rememberMe = $state(false);
  let showPassword = $state(false);
  let isLoading = $state(false);
  let verificationMessage = $state('');
  let loginInProgress = $state(false);

  // Turnstile state
  let turnstileToken = $state('');
  let isTurnstileVerified = $state(false);

  // Check for verification messages from URL params
  onMount(() => {
    const message = $page.url.searchParams.get('message');
    const urlEmail = $page.url.searchParams.get('email');
    
    if (urlEmail) {
      email = urlEmail;
    }
    
    if (message) {
      switch (message) {
        case 'verified':
          verificationMessage = '✅ Email verified successfully! Please log in to access your account.';
          toast.success('Email verified successfully!');
          break;
        case 'already_verified':
          verificationMessage = 'ℹ️ Your email is already verified. Please log in to continue.';
          break;
        case 'failed':
          verificationMessage = '⚠️ Email verification failed. Please try logging in or request a new verification link.';
          break;
        default:
          break;
      }
    }

    // Check if already authenticated - auto redirect (but not during login)
    const unsubscribe = isAuthenticated.subscribe((authenticated) => {
      if (authenticated && !loginInProgress) {
        const returnTo = $page.url.searchParams.get('return') || '/dashboard';
        toast.success('Already signed in, redirecting...');
        goto(returnTo);
      }
    });

    return unsubscribe;
  });

  // Handle Turnstile verification
  function handleTurnstileVerified(event: CustomEvent) {
    turnstileToken = event.detail.token;
    isTurnstileVerified = true;
    toast.success('Security verification completed');
  }

  function handleTurnstileError(event: CustomEvent) {
    console.error('Turnstile error:', event.detail.error);
    toast.error('Security verification failed. Please try again.');
    isTurnstileVerified = false;
    turnstileToken = '';
  }

  function handleTurnstileExpired() {
    isTurnstileVerified = false;
    turnstileToken = '';
    toast.warning('Security verification expired. Please verify again.');
  }

  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    if (!isTurnstileVerified || !turnstileToken) {
      toast.error('Please complete security verification first');
      return;
    }

    isLoading = true;
    loginInProgress = true;

    try {
      const result = await authActions.signIn(email, password, rememberMe);
      
      if (result.error) {
        toast.error(result.error.message || 'Login failed');
        // Reset Turnstile on failed login
        isTurnstileVerified = false;
        turnstileToken = '';
        return;
      }

      toast.success('Login successful!');
      
      const returnTo = $page.url.searchParams.get('return') || '/dashboard';
      goto(returnTo);
      
    } catch (error) {
      console.error('Login error:', error);
      toast.error('An unexpected error occurred');
      // Reset Turnstile on error
      isTurnstileVerified = false;
      turnstileToken = '';
    } finally {
      isLoading = false;
      // Small delay before allowing auto-redirect check again
      setTimeout(() => {
        loginInProgress = false;
      }, 1000);
    }
  }
</script>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4">
  <div class="w-full max-w-md space-y-8">
    <div class="text-center">
      <h2 class="text-3xl font-bold">Sign in to your account</h2>
    </div>

    <!-- Verification Message -->
    {#if verificationMessage}
      <div class="p-3 rounded-md {verificationMessage.includes('✅') ? 'bg-green-50 text-green-800 border border-green-200' : 
                   verificationMessage.includes('ℹ️') ? 'bg-blue-50 text-blue-800 border border-blue-200' : 
                   'bg-yellow-50 text-yellow-800 border border-yellow-200'}">
        <p class="text-sm text-center">{verificationMessage}</p>
      </div>
    {/if}

    <form class="space-y-4" onsubmit={handleSubmit}>
      <div class="space-y-2">
        <label for="email" class="text-sm font-medium">Email</label>
        <Input
          id="email"
          type="email"
          bind:value={email}
          placeholder="Enter your email"
          disabled={isLoading}
        />
      </div>

      <div class="space-y-2">
        <label for="password" class="text-sm font-medium">Password</label>
        <div class="relative">
          <Input
            id="password"
            type={showPassword ? 'text' : 'password'}
            bind:value={password}
            placeholder="Enter your password"
            disabled={isLoading}
            class="pr-10"
          />
          <button
            type="button"
            class="absolute inset-y-0 right-0 flex items-center pr-3"
            onclick={() => showPassword = !showPassword}
          >
            {#if showPassword}
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M14.12 14.12l1.415 1.415M14.12 14.12L9.878 9.878m4.242 4.242L19.5 19.5m-15-15l15 15" />
              </svg>
            {:else}
              <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            {/if}
          </button>
        </div>
      </div>

      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <Checkbox 
            id="remember-me" 
            bind:checked={rememberMe}
            disabled={isLoading}
          />
          <label for="remember-me" class="text-sm font-medium cursor-pointer">
            Remember me for 7 days
          </label>
        </div>

        <div class="text-sm">
          <a href="/auth/forgot-password" class="font-medium text-primary hover:underline">
            Forgot your password?
          </a>
        </div>
      </div>

      <!-- Turnstile Security Verification -->
      <div class="border-t pt-4">
        <TurnstileWrapper
          showTitle={false}
          on:verified={handleTurnstileVerified}
          on:error={handleTurnstileError}
          on:expired={handleTurnstileExpired}
        />
      </div>

      <Button type="submit" class="w-full" disabled={isLoading || !isTurnstileVerified}>
        {#if isLoading}
          <svg class="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Signing in...
        {:else}
          Sign in
        {/if}
      </Button>

      <div class="text-center">
        <p class="text-sm text-muted-foreground">
          Don't have an account?
          <a href="/auth/register" class="font-medium text-primary hover:underline">
            Create a new account
          </a>
        </p>
      </div>
    </form>
  </div>
</div>
