<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { authActions } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';
  import { onMount } from 'svelte';
  
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import TurnstileWrapper from '$lib/components/TurnstileWrapper.svelte';

  let password = $state('');
  let confirmPassword = $state('');
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);
  let token = $state('');

  // Turnstile state
  let turnstileToken = $state('');
  let isTurnstileVerified = $state(false);

  onMount(() => {
    token = $page.url.searchParams.get('token') || '';
    
    if (!token) {
      toast.error('Invalid reset link');
      goto('/auth/forgot-password');
    }
  });

  // Handle Turnstile verification
  function handleTurnstileVerified(event: CustomEvent) {
    turnstileToken = event.detail.token;
    isTurnstileVerified = true;
    toast.success('Security verification completed');
  }

  function handleTurnstileError(event: CustomEvent) {
    console.error('Turnstile error:', event.detail.error);
    toast.error('Security verification failed. Please try again.');
    isTurnstileVerified = false;
    turnstileToken = '';
  }

  function handleTurnstileExpired() {
    isTurnstileVerified = false;
    turnstileToken = '';
    toast.warning('Security verification expired. Please verify again.');
  }

  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!password || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      toast.error('Password must be at least 8 characters long');
      return;
    }

    if (!isTurnstileVerified || !turnstileToken) {
      toast.error('Please complete security verification first');
      return;
    }

    isLoading = true;

    try {
      const result = await authActions.resetPassword(password, token);
      
      if (result.error) {
        toast.error(result.error.message || 'Failed to reset password');
        // Reset Turnstile on failure
        isTurnstileVerified = false;
        turnstileToken = '';
        return;
      }

      toast.success('Password reset successfully! You can now sign in with your new password.');
      goto('/auth/login');
      
    } catch (error) {
      console.error('Reset password error:', error);
      toast.error('An unexpected error occurred');
      // Reset Turnstile on error
      isTurnstileVerified = false;
      turnstileToken = '';
    } finally {
      isLoading = false;
    }
  }
</script>

<svelte:head>
  <title>Reset Password - SourceFlex</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-background py-12 px-4">
  <div class="w-full max-w-md space-y-8">
    <div class="text-center">
      <h2 class="text-3xl font-bold">Set new password</h2>
      <p class="mt-2 text-sm text-muted-foreground">
        Enter your new password below
      </p>
    </div>

    <form class="space-y-6" onsubmit={handleSubmit}>
      <div class="space-y-4">
        <div class="space-y-2">
          <label for="password" class="text-sm font-medium">New Password</label>
          <div class="relative">
            <Input
              id="password"
              type={showPassword ? 'text' : 'password'}
              bind:value={password}
              placeholder="Enter new password"
              disabled={isLoading}
              class="pr-10"
            />
            <button
              type="button"
              class="absolute inset-y-0 right-0 flex items-center pr-3"
              onclick={() => showPassword = !showPassword}
            >
              {#if showPassword}
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M14.12 14.12l1.415 1.415M14.12 14.12L9.878 9.878m4.242 4.242L19.5 19.5m-15-15l15 15" />
                </svg>
              {:else}
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              {/if}
            </button>
          </div>
          <p class="text-xs text-muted-foreground">
            Must be at least 8 characters long
          </p>
        </div>

        <div class="space-y-2">
          <label for="confirm-password" class="text-sm font-medium">Confirm New Password</label>
          <div class="relative">
            <Input
              id="confirm-password"
              type={showConfirmPassword ? 'text' : 'password'}
              bind:value={confirmPassword}
              placeholder="Confirm new password"
              disabled={isLoading}
              class="pr-10"
            />
            <button
              type="button"
              class="absolute inset-y-0 right-0 flex items-center pr-3"
              onclick={() => showConfirmPassword = !showConfirmPassword}
            >
              {#if showConfirmPassword}
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L8.464 8.464M14.12 14.12l1.415 1.415M14.12 14.12L9.878 9.878m4.242 4.242L19.5 19.5m-15-15l15 15" />
                </svg>
              {:else}
                <svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              {/if}
            </button>
          </div>
        </div>
      </div>

      <!-- Turnstile Security Verification -->
      <div class="border-t pt-4">
        <TurnstileWrapper
          title="Security Verification"
          description="Please verify you're human to reset your password."
          showTitle={true}
          on:verified={handleTurnstileVerified}
          on:error={handleTurnstileError}
          on:expired={handleTurnstileExpired}
        />
      </div>

      <Button type="submit" class="w-full" disabled={isLoading || !token || !isTurnstileVerified}>
        {#if isLoading}
          <svg class="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Updating password...
        {:else}
          Update password
        {/if}
      </Button>

      <div class="text-center">
        <a href="/auth/login" class="text-sm font-medium text-primary hover:underline">
          Back to login
        </a>
      </div>
    </form>
  </div>
</div>
