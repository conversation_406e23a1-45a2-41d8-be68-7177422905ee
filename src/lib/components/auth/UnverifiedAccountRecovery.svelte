<script lang="ts">
  import { goto } from '$app/navigation';
  import { resendVerificationEmail } from '$lib/utils/auth-recovery.js';
  import toast from 'svelte-5-french-toast';

  interface Props {
    email: string;
    attemptsUsed?: number;
    maxAttempts?: number;
    onBackToForm?: () => void;
  }

  let { email, attemptsUsed = 0, maxAttempts = 3, onBackToForm }: Props = $props();

  let isResending = $state(false);
  let isEmailSent = $state(false);
  let canResend = $derived(attemptsUsed < maxAttempts);

  async function handleResendVerification() {
    if (!canResend || isResending) return;

    isResending = true;
    
    try {
      const result = await resendVerificationEmail(email);
      
      if (result.success) {
        isEmailSent = true;
        toast.success(result.message);
        attemptsUsed = result.attemptsUsed;
      } else {
        toast.error(result.message);
        attemptsUsed = result.attemptsUsed;
      }
    } catch (error) {
      console.error('Resend verification error:', error);
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      isResending = false;
    }
  }

  function handleGoToLogin() {
    goto('/auth/login');
  }
</script>

{#if isEmailSent}
  <!-- Success State -->
  <div class="bg-green-50 border border-green-200 rounded-md p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-green-800">
          Verification email sent!
        </h3>
        <div class="mt-2 text-sm text-green-700">
          <p>
            We've sent a new verification link to <strong>{email}</strong>. 
            Please check your inbox and spam folder.
          </p>
        </div>
        <div class="mt-4 space-y-2">
          <button
            onclick={handleGoToLogin}
            class="text-sm font-medium text-green-800 hover:text-green-600 underline"
          >
            Go to Login
          </button>
          {#if onBackToForm}
            <span class="text-green-600">•</span>
            <button
              onclick={onBackToForm}
              class="text-sm font-medium text-green-800 hover:text-green-600 underline"
            >
              Back to Registration
            </button>
          {/if}
        </div>
      </div>
    </div>
  </div>

  <!-- Email Tips -->
  <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mt-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-blue-800">
          Tips for receiving your email:
        </h3>
        <div class="mt-2 text-sm text-blue-700">
          <ul class="list-disc list-inside space-y-1">
            <li>Check your spam/junk folder</li>
            <li>Wait up to 5 minutes for delivery</li>
            <li>Add <EMAIL> to your contacts</li>
            <li>Check that your email address is correct</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
{:else}
  <!-- Recovery Options -->
  <div class="bg-orange-50 border border-orange-200 rounded-md p-4">
    <div class="flex">
      <div class="flex-shrink-0">
        <svg class="h-5 w-5 text-orange-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
      </div>
      <div class="ml-3">
        <h3 class="text-sm font-medium text-orange-800">
          Account needs verification
        </h3>
        <div class="mt-2 text-sm text-orange-700">
          <p>
            We found an account with <strong>{email}</strong> that hasn't been verified yet. 
            We can send you a new verification link to complete your account setup.
          </p>
        </div>
        
        <!-- Rate Limiting Info -->
        {#if attemptsUsed > 0}
          <div class="mt-3 text-sm text-orange-600 bg-orange-100 p-2 rounded">
            <p>Verification emails sent today: {attemptsUsed}/{maxAttempts}</p>
            {#if !canResend}
              <p class="font-medium">
                Daily limit reached. Please contact support for assistance.
              </p>
            {/if}
          </div>
        {/if}

        <div class="mt-4 space-x-3">
          <button
            onclick={handleResendVerification}
            disabled={isResending || !canResend}
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {#if isResending}
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Sending...
            {:else if !canResend}
              Daily Limit Reached
            {:else}
              Send Verification Email
            {/if}
          </button>
          
          <button
            onclick={handleGoToLogin}
            class="inline-flex items-center px-3 py-2 border border-orange-300 text-sm leading-4 font-medium rounded-md text-orange-700 bg-white hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Try Login Instead
          </button>
          
          {#if onBackToForm}
            <button
              onclick={onBackToForm}
              class="text-sm font-medium text-orange-600 hover:text-orange-500"
            >
              Back to Registration
            </button>
          {/if}
        </div>
      </div>
    </div>
  </div>
{/if}
