import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { nhost } from './nhost.js';
import type { NhostSession } from '@nhost/nhost-js';
import type { UserProfile, AuthResult, SignUpOptions } from '$lib/types/auth.js';

// Enhanced session management configuration
const SESSION_CONFIG = {
  IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes in milliseconds
  WARNING_TIME: 5 * 60 * 1000,  // Show warning 5 minutes before timeout
  REFRESH_INTERVAL: 10 * 60 * 1000, // Refresh token every 10 minutes
  ACTIVITY_EVENTS: ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
};

// Session state stores
export const session = writable<NhostSession | null>(null);
export const userProfile = writable<UserProfile | null>(null);
export const sessionWarning = writable<boolean>(false);
export const sessionTimeRemaining = writable<number>(SESSION_CONFIG.IDLE_TIMEOUT);

// Derived stores
export const isAuthenticated = derived(session, ($session) => {
  return $session?.user && !$session?.user?.isAnonymous;
});

export const userRole = derived(session, ($session) => {
  if (!$session?.user) return null;
  return $session.user.defaultRole || 'user';
});

export const userRoles = derived(session, ($session) => {
  if (!$session?.user) return [];
  const roles = ($session.user as any).roles || [];
  return roles;
});

// Session management class
class SessionManager {
  private idleTimer: number | null = null;
  private warningTimer: number | null = null;
  private refreshTimer: number | null = null;
  private lastActivity: number = Date.now();
  private warningShown: boolean = false;

  constructor() {
    if (!browser) return;
    
    this.setupActivityListeners();
    this.startTimers();
  }

  private setupActivityListeners() {
    if (!browser || typeof document === 'undefined') return;
    
    SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, () => this.updateActivity(), { passive: true });
    });
  }

  private updateActivity() {
    this.lastActivity = Date.now();
    
    if (this.warningShown) {
      this.hideWarning();
    }
    
    this.resetTimers();
  }

  private startTimers() {
    this.resetTimers();
    this.startRefreshTimer();
  }

  private resetTimers() {
    // Clear existing timers
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    
    // Start warning timer
    this.warningTimer = window.setTimeout(() => {
      this.showWarning();
    }, SESSION_CONFIG.IDLE_TIMEOUT - SESSION_CONFIG.WARNING_TIME);
    
    // Start idle timeout timer
    this.idleTimer = window.setTimeout(() => {
      this.handleIdleTimeout();
    }, SESSION_CONFIG.IDLE_TIMEOUT);
    
    // Update remaining time
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private showWarning() {
    this.warningShown = true;
    sessionWarning.set(true);
    
    // Start countdown
    const startTime = Date.now();
    const countdownInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = SESSION_CONFIG.WARNING_TIME - elapsed;
      
      if (remaining <= 0) {
        clearInterval(countdownInterval);
        return;
      }
      
      sessionTimeRemaining.set(remaining);
    }, 1000);
  }

  private hideWarning() {
    this.warningShown = false;
    sessionWarning.set(false);
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private async handleIdleTimeout() {
    console.log('Session idle timeout - signing out');
    await authActions.signOut();
  }

  private startRefreshTimer() {
    if (this.refreshTimer) clearInterval(this.refreshTimer);
    
    this.refreshTimer = window.setInterval(async () => {
      const currentSession = nhost.auth.getSession();
      if (currentSession?.accessToken) {
        try {
          // Refresh token if session exists
          await nhost.auth.refreshSession();
          console.log('Token refreshed successfully');
        } catch (error) {
          console.error('Token refresh failed:', error);
          await authActions.signOut();
        }
      }
    }, SESSION_CONFIG.REFRESH_INTERVAL);
  }

  public extendSession() {
    this.updateActivity();
  }

  public destroy() {
    if (!browser) return;
    
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    if (this.refreshTimer) clearInterval(this.refreshTimer);
    
    if (typeof document !== 'undefined') {
      SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
        document.removeEventListener(event, () => this.updateActivity());
      });
    }
  }
}

// Initialize session manager
let sessionManager: SessionManager | null = null;

// Browser initialization
if (browser) {
  const initializeAuth = async () => {
    try {
      const initialSession = nhost.auth.getSession();
      session.set(initialSession);
      
      if (initialSession?.user) {
        sessionManager = new SessionManager();
      }
      
      console.log('Enhanced auth initialized with session:', !!initialSession?.user);
    } catch (error) {
      console.error('Auth initialization error:', error);
      session.set(null);
    }
  };

  // Listen to auth state changes
  nhost.auth.onAuthStateChanged((event: string, nhostSession: any) => {
    console.log('Auth state changed:', event, nhostSession?.user?.id);
    session.set(nhostSession);
    
    if (event === 'SIGNED_IN' && nhostSession?.user) {
      // Start session management
      if (!sessionManager) {
        sessionManager = new SessionManager();
      }
    } else if (event === 'SIGNED_OUT') {
      // Clean up session management
      userProfile.set(null);
      sessionWarning.set(false);
      sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
      
      if (sessionManager) {
        sessionManager.destroy();
        sessionManager = null;
      }
    }
  });

  initializeAuth();
}

// Enhanced auth actions
export const authActions = {
  signUp: async (email: string, password: string, options: SignUpOptions = {}): Promise<AuthResult> => {
    const result = await nhost.auth.signUp({
      email,
      password,
      ...options
    });
    return result as AuthResult;
  },

  signIn: async (email: string, password: string, rememberMe: boolean = false): Promise<AuthResult> => {
    // Store device info for tracking (only in browser)
    let deviceInfo = {};
    if (browser && typeof navigator !== 'undefined') {
      deviceInfo = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        timestamp: new Date().toISOString()
      };
    }

    const result = await nhost.auth.signIn({
      email,
      password
    });

    if (result.session?.user && browser) {
      // Track login
      console.log('User signed in with remember me:', rememberMe);
      
      // Store session preferences
      if (typeof localStorage !== 'undefined') {
        localStorage.setItem('sf_remember_me', rememberMe.toString());
      }
    }

    return result as AuthResult;
  },

  signOut: async (): Promise<AuthResult> => {
    // Clean up session warning
    sessionWarning.set(false);
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
    
    const result = await nhost.auth.signOut();
    userProfile.set(null);
    session.set(null);
    
    // Clean up session manager
    if (sessionManager) {
      sessionManager.destroy();
      sessionManager = null;
    }
    
    // Clean up session preferences
    if (browser && typeof localStorage !== 'undefined') {
      localStorage.removeItem('sf_remember_me');
      window.location.href = '/';
    }
    
    return result as AuthResult;
  },

  // Extend current session (for warning dialog)
  extendSession: () => {
    if (sessionManager) {
      sessionManager.extendSession();
    }
  },

  // Force session termination
  forceLogout: () => {
    session.set(null);
    userProfile.set(null);
    sessionWarning.set(false);
    
    if (sessionManager) {
      sessionManager.destroy();
      sessionManager = null;
    }
    
    if (browser && typeof localStorage !== 'undefined' && typeof sessionStorage !== 'undefined') {
      localStorage.clear();
      sessionStorage.clear();
      window.location.href = '/';
    }
  },

  sendPasswordResetEmail: async (email: string): Promise<AuthResult> => {
    const result = await nhost.auth.resetPassword({ email });
    return result as AuthResult;
  },

  resetPassword: async (password: string, passwordResetToken: string): Promise<AuthResult> => {
    const result = await nhost.auth.changePassword({
      newPassword: password,
      ticket: passwordResetToken
    });
    return result as AuthResult;
  },

  // Check if user has remember me enabled
  isRememberMeEnabled: (): boolean => {
    if (!browser || typeof localStorage === 'undefined') return false;
    return localStorage.getItem('sf_remember_me') === 'true';
  }
};

// Export session configuration for components
export { SESSION_CONFIG };
