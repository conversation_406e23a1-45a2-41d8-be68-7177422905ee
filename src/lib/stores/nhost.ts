import { NhostClient } from '@nhost/nhost-js';
import { browser } from '$app/environment';

// Get environment variables - works in both dev and Cloudflare Workers
const getEnvVar = (key: string, fallback: string = '') => {
  if (browser) {
    // Client-side: use import.meta.env
    return import.meta.env[key] || fallback;
  }
  // Server-side: try different sources for Cloudflare Workers
  if (typeof globalThis !== 'undefined' && globalThis.process?.env) {
    return globalThis.process.env[key] || fallback;
  }
  return fallback;
};

const NHOST_SUBDOMAIN = getEnvVar('PUBLIC_NHOST_SUBDOMAIN', 'pttthnqikxdsxmeccqho');
const NHOST_REGION = getEnvVar('PUBLIC_NHOST_REGION', 'us-west-2');

// Create a safe nHost client - handle both SSR and browser
export const nhost = browser 
  ? new NhostClient({
      subdomain: NHOST_SUBDOMAIN,
      region: NHOST_REGION,
      autoRefreshToken: true
    }) 
  : {
      // Safe mock for SSR
      auth: {
        getSession: () => null,
        onAuthStateChanged: () => {},
        signUp: () => Promise.resolve({ session: null, error: null }),
        signIn: () => Promise.resolve({ session: null, error: null }),
        signOut: () => Promise.resolve({ error: null }),
        resetPassword: () => Promise.resolve({ error: null }),
        changePassword: () => Promise.resolve({ error: null }),
        refreshSession: () => Promise.resolve({ session: null, error: null })
      }
    } as any;
