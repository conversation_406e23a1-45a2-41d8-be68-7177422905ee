# Cloudflare Workers configuration for SourceFlex
name = "sourceflex"
main = ".svelte-kit/cloudflare/_worker.js"
compatibility_date = "2024-07-17"

# Static assets configuration for Workers
[assets]
directory = ".svelte-kit/cloudflare"
binding = "ASSETS"

# Environment variables
[vars]
NODE_ENV = "production"
PUBLIC_TURNSTILE_SITE_KEY = "0x4AAAAAABllltaE5hyR1BQ5"
TURNSTILE_SECRET_KEY = "0x4AAAAAABllljBJ5eJeSF6IE1v1h9sJocQ"

# Enable Node.js compatibility if needed
compatibility_flags = ["nodejs_compat"]

# Observability configuration for monitoring and debugging
[observability.logs]
enabled = true

# Optional: Custom domain configuration (configure later in dashboard)
# [route]
# pattern = "sourceflex.io"
# zone_name = "sourceflex.io"
